from threading import Event, Lock
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
import time
import requests
import json
from urllib.parse import parse_qs, urlparse
from log import logger


# 目标URL和API配置
url = "https://sgm-m.jd.com/h5/"
rushTime = 1756975709000

# h5st 签名服务配置
H5ST_SERVICE_URL = "http://192.168.241.168:3001/h5st"

# 京东API配置
JD_API_BASE = "https://api.m.jd.com/client.action"
FUNCTION_ID = "newBabelAwardCollection"
APP_ID = "babelh5"

# 从原始API中提取body参数
ACTIVITY_BODY = {"activityId":"3XgsQ4Caupu9ut9814NggDCbjk4L","from":"H5node","scene":"1","args":"key=C7964E0C9BAB491A89419FB7D10D721D35AA4D23664AADA4718FDD634FDDAE255E33820A06A81C5FF77571C48BCEDFC9_bingo,roleId=A353253B2ECBCF65C26C21B29AEA635625274A4FAE1F910C5028DEDC7277097CF8FFF071906003F040A54E0FD8F4E57A88687043C304B038B16D032344B8DDC28E738EDDAFC5073101C6AEBDF413FD056BA20307F2A2E08949550A5D584AB15AEDA44520057324CB51F5972D935646979A9728255886DEA1B6ED86959A82733714E58EF4493CD0F671024FE9E30D08EC20AFD5E93A092089103724115C15796A_bingo"}

cookie = [
  "pt_key=AAJoTsn8ADAGKPUOWwvxwuMCM8KZtLUE7ccCBgTny3Gp3jta-Gv1_vHRsPOYsrlMUk836FcOpp8; pt_pin=jd_vSVZIXuMHVxW;",
]
 
# 线程控制配置
MAX_THREADS = 1  # 最大线程数
TIMEOUT_MS = 10  # 超时时间（毫秒）
RUSH_PREPARE_TIME = 5500  # 提前准备时间（毫秒）

# 线程安全控制
stop_event = Event()
request_lock = Lock()
success_count = 0
account_stats = {}  # 每个账号的统计信息

def extract_pt_pin(cookie_str):
    """从cookie字符串中提取pt_pin"""
    try:
        for part in cookie_str.split(';'):
            part = part.strip()
            if part.startswith('pt_pin='):
                return part.split('=')[1]
        return "未知账号"
    except Exception:
        return "未知账号"


def get_h5st_signature(pt_pin):
    """获取h5st签名"""
    try:
        h5st_payload = {
            "version": "5.2.0",
            "pin": pt_pin,
            "ua": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
            "appId": APP_ID,
            "body": {
                "functionId": FUNCTION_ID,
                "appid": APP_ID,
                "body": ACTIVITY_BODY
            }
        }

        headers = {
            "Content-Type": "application/json"
        }

        response = requests.post(H5ST_SERVICE_URL, json=h5st_payload, headers=headers, timeout=3)

        if response.status_code == 200:
            result = response.json()
            if result.get("code") == 200 and "body" in result and "h5st" in result["body"]:
                h5st_value = result["body"]["h5st"].get("h5st")
                if h5st_value:
                    logger.info(f"获取h5st签名成功: {h5st_value[:50]}...")
                    return h5st_value
                else:
                    logger.error("h5st响应中缺少h5st字段")
            else:
                logger.error(f"h5st服务返回错误: {result}")
        else:
            logger.error(f"h5st服务请求失败: {response.status_code}")

    except Exception as e:
        logger.error(f"获取h5st签名异常: {e}")

    return None

class JDRushBuyer:
    """京东抢购类 - 多账号优化版本"""

    def __init__(self, cookie_str, account_id):
        self.cookie_str = cookie_str
        self.pt_pin = extract_pt_pin(cookie_str)
        self.account_id = account_id
        self.h5st_signature = None  # 存储h5st签名

        self.headers = {
            "Host": "api.m.jd.com",
            "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
            "Referer": "https://trade.jd.com/",
            "Cookie": cookie_str
        }
        self.session = requests.Session()
        self.session.headers.update(self.headers)

        # 初始化账号统计
        with request_lock:
            if self.pt_pin not in account_stats:
                account_stats[self.pt_pin] = {'success': 0, 'failed': 0}

    def prepare_h5st(self):
        """提前获取h5st签名"""
        logger.info(f"账号[{self.pt_pin}] 开始获取h5st签名...")
        self.h5st_signature = get_h5st_signature(self.pt_pin)
        if self.h5st_signature:
            logger.info(f"账号[{self.pt_pin}] h5st签名获取成功")
            return True
        else:
            logger.error(f"账号[{self.pt_pin}] h5st签名获取失败")
            return False

    def make_request(self, thread_id):
        """执行单次请求"""
        global success_count

        if stop_event.is_set():
            return None

        # 检查是否有h5st签名
        if not self.h5st_signature:
            logger.error(f"账号[{self.pt_pin}] 线程{thread_id} - 缺少h5st签名，跳过请求")
            return None

        try:
            start_time = time.time() * 1000

            # 构建POST请求数据
            post_data = {
                'functionId': FUNCTION_ID,
                'appid': APP_ID,
                'body': json.dumps(ACTIVITY_BODY),
                'client': 'wh5',
                'h5st': self.h5st_signature
            }

            # 发送POST请求
            response = self.session.post(JD_API_BASE, data=post_data, timeout=5)
            end_time = time.time() * 1000

            with request_lock:
                success_count += 1
                account_stats[self.pt_pin]['success'] += 1
                logger.info(f"账号[{self.pt_pin}] 线程{thread_id} - 请求成功 - 耗时: {end_time - start_time:.2f}ms - 响应: {response.text[:100]}...")

            return {
                'thread_id': thread_id,
                'account': self.pt_pin,
                'status_code': response.status_code,
                'response_time': end_time - start_time,
                'success': True
            }

        except Exception as e:
            with request_lock:
                account_stats[self.pt_pin]['failed'] += 1
            logger.error(f"账号[{self.pt_pin}] 线程{thread_id} - 请求失败: {e}")
            return {
                'thread_id': thread_id,
                'account': self.pt_pin,
                'error': str(e),
                'success': False
            }

def execute_rush_requests(buyers, start_time):
    """执行抢购请求的核心函数 - 多账号版本"""
    global success_count

    logger.info(f"开始抢购执行 - 目标时间: {rushTime}")
    logger.info(f"参与账号数量: {len(buyers)}")

    # 计算每个账号的线程数
    threads_per_account = MAX_THREADS // len(buyers)
    if threads_per_account == 0:
        threads_per_account = 1

    # 使用线程池执行请求
    with ThreadPoolExecutor(max_workers=MAX_THREADS) as executor:
        futures = []

        # 持续提交任务直到超时
        while True:
            current_time = int(time.time() * 1000)

            # 检查是否超过超时时间
            if current_time - start_time >= TIMEOUT_MS:
                logger.info(f"达到超时限制 {TIMEOUT_MS}ms，停止提交新任务")
                stop_event.set()
                break

            # 为每个账号提交请求任务
            for buyer in buyers:
                for i in range(threads_per_account):
                    if not stop_event.is_set():
                        thread_id = f"{buyer.account_id}-{i+1}"
                        future = executor.submit(buyer.make_request, thread_id)
                        futures.append(future)

            # 短暂休眠避免CPU过度占用
            time.sleep(0.001)  # 1毫秒

        # 等待所有任务完成或超时
        logger.info("等待所有请求完成...")
        completed_count = 0

        for future in as_completed(futures, timeout=5):
            try:
                result = future.result()
                if result and result.get('success'):
                    completed_count += 1
            except Exception as e:
                logger.error(f"任务执行异常: {e}")

        # 输出每个账号的统计信息
        logger.info("=== 各账号执行统计 ===")
        for pt_pin, stats in account_stats.items():
            logger.info(f"账号[{pt_pin}] - 成功: {stats['success']}次, 失败: {stats['failed']}次")

        logger.info(f"抢购完成 - 总成功请求数: {success_count}, 完成任务数: {completed_count}")


def wait_for_rush_time(buyers):
    """等待到达抢购时间，并在提前准备时间内获取h5st签名"""
    logger.info(f"等待抢购时间: {rushTime}")

    while True:
        current_timestamp = int(time.time() * 1000)
        time_diff = rushTime - current_timestamp

        # 检查时间是否已过
        if current_timestamp > rushTime:
            logger.warning('目标时间已过，无法执行抢购！')
            return False

        # 提前5.5秒进入精确等待并获取h5st签名
        if time_diff <= RUSH_PREPARE_TIME:
            logger.info(f"进入精确等待模式，剩余时间: {time_diff}ms")

            # 为所有账号获取h5st签名
            logger.info("开始为所有账号获取h5st签名...")
            h5st_success_count = 0
            for buyer in buyers:
                if buyer.prepare_h5st():
                    h5st_success_count += 1

            logger.info(f"h5st签名获取完成: {h5st_success_count}/{len(buyers)} 个账号成功")

            # 精确等待到目标时间
            while True:
                current_timestamp = int(time.time() * 1000)
                if current_timestamp >= rushTime:
                    logger.info(f"到达目标时间: {current_timestamp}")
                    return True

                # 微秒级休眠
                time.sleep(0.0001)  # 0.1毫秒
        else:
            # 距离目标时间较远，每秒检查一次，只在终端显示不记录日志
            print(f'距离抢购时间还有: {time_diff}ms ({time_diff/1000:.1f}秒)')
            time.sleep(1)


def main():
    """主函数 - 多账号优化版本"""
    global success_count

    try:
        logger.info("=== 京东多账号抢购脚本启动 ===")
        logger.info(f"目标时间: {rushTime}")
        logger.info(f"最大线程数: {MAX_THREADS}")
        logger.info(f"超时时间: {TIMEOUT_MS}ms")
        logger.info(f"账号数量: {len(cookie)}")

        # 创建多个账号的抢购实例
        buyers = []
        for i, cookie_str in enumerate(cookie):
            pt_pin = extract_pt_pin(cookie_str)
            buyer = JDRushBuyer(cookie_str, i + 1)
            buyers.append(buyer)
            logger.info(f"账号{i+1}: {pt_pin}")

        # 等待到达抢购时间并获取h5st签名
        if not wait_for_rush_time(buyers):
            return

        # 记录开始时间
        start_time = int(time.time() * 1000)
        logger.info(f"开始执行抢购 - 开始时间: {start_time}")

        # 执行抢购请求
        execute_rush_requests(buyers, start_time)

        # 输出最终统计
        end_time = int(time.time() * 1000)
        total_time = end_time - start_time
        logger.info(f"=== 抢购结束 ===")
        logger.info(f"总耗时: {total_time}ms")
        logger.info(f"总成功请求数: {success_count}")

    except KeyboardInterrupt:
        logger.info("用户中断程序")
        stop_event.set()
    except Exception as e:
        logger.error(f"程序执行异常: {e}")
        stop_event.set()


if __name__=="__main__":
    main()